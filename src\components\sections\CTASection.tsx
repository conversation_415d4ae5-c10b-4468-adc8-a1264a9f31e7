import Link from "next/link";

export function CTASection() {
  return (
    <section className="py-16 md:py-24 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl"></div>

          <div className="relative z-10 text-center py-16 lg:py-24 px-8 lg:px-16">
            {/* Main Content */}
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold font-heading leading-tight mb-6">
                Ready to Transform Your{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Digital Vision</span>?
              </h2>
              <p className="text-xl text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto">
                Let's collaborate to create exceptional digital experiences that drive growth,
                engage users, and deliver measurable results for your business.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                <Link
                  href="/contact"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-lg"
                >
                  Start Your Project →
                </Link>
                <Link
                  href="/portfolio"
                  className="bg-white hover:bg-gray-50 text-blue-600 font-semibold py-4 px-8 rounded-lg border-2 border-blue-600 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-lg"
                >
                  View Our Work
                </Link>
              </div>

              {/* Contact Options */}
              <div className="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    💬
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Quick Chat</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Get instant answers to your questions via WhatsApp
                  </p>
                  <a
                    href="https://wa.me/1234567890"
                    className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors duration-200"
                  >
                    Chat on WhatsApp →
                  </a>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    📅
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Schedule Call</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Book a free consultation to discuss your project
                  </p>
                  <a
                    href="#"
                    className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors duration-200"
                  >
                    Book Free Call →
                  </a>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    ✉️
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Send Email</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Detailed project inquiry via email
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-purple-600 hover:text-purple-700 font-medium text-sm transition-colors duration-200"
                  >
                    Send Email →
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
