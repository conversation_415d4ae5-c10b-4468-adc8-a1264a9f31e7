import Link from "next/link";

const services = [
  {
    title: "Web Design",
    description: "Stunning, responsive websites that convert visitors into customers with modern design principles and user-centric approach.",
    features: ["Responsive Design", "UI/UX Optimization", "Performance Focus", "SEO Ready"],
    href: "/services/web-design",
    bgColor: "bg-blue-50",
    iconColor: "text-blue-600",
    icon: "🖥️"
  },
  {
    title: "Mobile Apps",
    description: "Native and cross-platform mobile applications that deliver exceptional user experiences across iOS and Android.",
    features: ["Native Development", "Cross-Platform", "App Store Optimization", "Push Notifications"],
    href: "/services/mobile-apps",
    bgColor: "bg-green-50",
    iconColor: "text-green-600",
    icon: "📱"
  },
  {
    title: "Web Applications",
    description: "Scalable web applications built with cutting-edge technologies for optimal performance and user engagement.",
    features: ["Progressive Web Apps", "Real-time Features", "Cloud Integration", "API Development"],
    href: "/services/web-apps",
    bgColor: "bg-purple-50",
    iconColor: "text-purple-600",
    icon: "🌐"
  },
  {
    title: "Software Solutions",
    description: "Custom software development tailored to your business needs with enterprise-grade security and scalability.",
    features: ["Custom Development", "System Integration", "Legacy Modernization", "DevOps"],
    href: "/services/software-solutions",
    bgColor: "bg-orange-50",
    iconColor: "text-orange-600",
    icon: "💻"
  },
];

export function ServicesSection() {
  return (
    <section id="services" className="py-16 md:py-24 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold font-heading leading-tight mb-6">
            Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We offer comprehensive digital solutions to transform your ideas into powerful,
            scalable applications that drive business growth and user engagement.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 mb-20">
          {services.map((service, index) => (
            <div key={service.title} className="group">
              <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 transition-all duration-300 hover:shadow-lg hover:transform hover:-translate-y-2 h-full">
                {/* Icon */}
                <div className={`w-16 h-16 ${service.bgColor} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-2xl">{service.icon}</span>
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-2 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* CTA */}
                <Link
                  href={service.href}
                  className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors duration-200"
                >
                  Learn More →
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Start Your Project?
          </h3>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's discuss your requirements and create a solution that perfectly fits your needs and budget.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
              Get Free Consultation
            </Link>
            <Link href="/portfolio" className="bg-white hover:bg-gray-50 text-blue-600 font-semibold py-3 px-6 rounded-lg border-2 border-blue-600 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
              View Our Work
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
