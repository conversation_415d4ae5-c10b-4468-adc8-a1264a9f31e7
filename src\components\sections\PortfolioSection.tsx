import Link from "next/link";

export function PortfolioSection() {
  return (
    <section id="portfolio" className="py-16 md:py-24 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold font-heading leading-tight mb-6">
            Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Portfolio</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Explore our latest projects and see how we've helped businesses transform
            their digital presence with innovative solutions.
          </p>
          <Link
            href="/portfolio"
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
          >
            View Full Portfolio →
          </Link>
        </div>
      </div>
    </section>
  );
}
